import React from 'react'

const Pricing = () => {
  const plans = [
    {
      name: "Basic",
      price: "$29",
      period: "/month",
      description: "Perfect for beginners starting their trading journey",
      features: [
        "Access to basic courses",
        "Community forum access",
        "Email support",
        "Basic trading guides",
        "Mobile app access"
      ],
      popular: false,
      color: "gray"
    },
    {
      name: "Premium",
      price: "$99",
      period: "/month",
      description: "Most popular choice for serious traders",
      features: [
        "Access to all courses",
        "Premium Telegram signals",
        "1-on-1 mentoring sessions",
        "Advanced trading strategies",
        "Priority support",
        "Market analysis reports",
        "Live trading sessions",
        "Risk management tools"
      ],
      popular: true,
      color: "blue"
    },
    {
      name: "VIP",
      price: "$199",
      period: "/month",
      description: "For professional traders seeking maximum results",
      features: [
        "Everything in Premium",
        "Personal trading coach",
        "Custom trading strategies",
        "Direct access to expert traders",
        "Weekly live sessions",
        "Portfolio review",
        "Exclusive market insights",
        "Priority Telegram channel"
      ],
      popular: false,
      color: "purple"
    }
  ]

  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <section id="pricing" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">Choose Your Plan</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Select the perfect plan for your trading journey. All plans include access to our private Telegram channel.
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {plans.map((plan, index) => (
            <div key={index} className={`card relative ${plan.popular ? 'ring-2 ring-blue-500 transform scale-105' : ''} hover:shadow-xl transition-all duration-300`}>
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-blue-500 text-white px-6 py-2 rounded-full text-sm font-bold">
                    🔥 Most Popular
                  </span>
                </div>
              )}
              
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                <p className="text-gray-600 mb-4">{plan.description}</p>
                <div className="flex items-center justify-center mb-4">
                  <span className={`text-5xl font-bold ${plan.color === 'blue' ? 'text-blue-600' : plan.color === 'purple' ? 'text-purple-600' : 'text-gray-700'}`}>
                    {plan.price}
                  </span>
                  <span className="text-gray-500 ml-2 text-lg">{plan.period}</span>
                </div>
                {plan.popular && (
                  <div className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium inline-block">
                    Save 30% - Limited Time
                  </div>
                )}
              </div>

              <ul className="space-y-4 mb-8">
                {plan.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-start">
                    <svg className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-gray-600">{feature}</span>
                  </li>
                ))}
              </ul>

              <button className={`w-full py-4 px-6 rounded-lg font-bold text-lg transition-all duration-300 transform hover:scale-105 ${
                plan.popular 
                  ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg' 
                  : plan.color === 'purple'
                  ? 'bg-purple-600 hover:bg-purple-700 text-white'
                  : 'bg-gray-200 hover:bg-gray-300 text-gray-800'
              }`}>
                {plan.popular ? '🚀 Start Premium' : 'Get Started'}
              </button>

              {plan.popular && (
                <div className="text-center mt-4">
                  <p className="text-sm text-gray-500">
                    💳 30-day money-back guarantee
                  </p>
                </div>
              )}
            </div>
          ))}
        </div>

        <div className="text-center mt-16">
          <div className="bg-blue-50 rounded-2xl p-8 max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">🎯 What You Get With Any Plan</h3>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 text-center">
              <div>
                <div className="text-3xl mb-2">📱</div>
                <h4 className="font-semibold text-gray-900">Telegram Access</h4>
                <p className="text-sm text-gray-600">Private channel with signals</p>
              </div>
              <div>
                <div className="text-3xl mb-2">🎓</div>
                <h4 className="font-semibold text-gray-900">Expert Training</h4>
                <p className="text-sm text-gray-600">Learn from professionals</p>
              </div>
              <div>
                <div className="text-3xl mb-2">📊</div>
                <h4 className="font-semibold text-gray-900">Market Analysis</h4>
                <p className="text-sm text-gray-600">Daily market insights</p>
              </div>
              <div>
                <div className="text-3xl mb-2">🤝</div>
                <h4 className="font-semibold text-gray-900">Community</h4>
                <p className="text-sm text-gray-600">Connect with traders</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Pricing
