import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../context/AuthContext'
import { useCurrency } from '../hooks/useCurrency'

const AdminPage = () => {
  const { user, logout } = useAuth()
  const navigate = useNavigate()
  const [activeTab, setActiveTab] = useState('overview')
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const { formatUSDToKSH, exchangeRate } = useCurrency()

  // Mock admin data - in real app, this would come from API
  const [stats] = useState({
    totalUsers: 1247,
    activeSubscriptions: 892,
    totalRevenue: 89250,
    pendingTransactions: 23,
    telegramMembers: 856,
    monthlyGrowth: 12.5
  })

  const [users, setUsers] = useState([
    {
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+254712345678',
      telegram: '@johndoe',
      plan: 'Premium',
      status: 'active',
      joinDate: '2024-01-15',
      expiryDate: '2024-02-15',
      lastPayment: '$99',
      telegramStatus: 'added'
    },
    {
      id: 2,
      name: '<PERSON> <PERSON>',
      email: '<EMAIL>',
      phone: '+254723456789',
      telegram: '@janesmith',
      plan: 'Basic',
      status: 'expired',
      joinDate: '2024-01-10',
      expiryDate: '2024-01-25',
      lastPayment: '$29',
      telegramStatus: 'removed'
    },
    {
      id: 3,
      name: 'Mike Johnson',
      email: '<EMAIL>',
      phone: '+254734567890',
      telegram: '@mikej',
      plan: 'VIP',
      status: 'active',
      joinDate: '2024-01-20',
      expiryDate: '2024-02-20',
      lastPayment: '$199',
      telegramStatus: 'added'
    }
  ])

  const [packages, setPackages] = useState([
    {
      id: 1,
      name: 'Basic',
      price: 29,
      duration: 30,
      features: ['Basic courses', 'Email support', 'Community access'],
      isActive: true,
      subscribers: 156
    },
    {
      id: 2,
      name: 'Premium',
      price: 99,
      duration: 30,
      features: ['All courses', 'Telegram signals', '1-on-1 mentoring', 'Priority support'],
      isActive: true,
      subscribers: 642
    },
    {
      id: 3,
      name: 'VIP',
      price: 199,
      duration: 30,
      features: ['Everything in Premium', 'Personal coach', 'Custom strategies', 'Weekly sessions'],
      isActive: true,
      subscribers: 94
    }
  ])

  const [transactions, setTransactions] = useState([
    {
      id: 'TXN001',
      user: 'John Doe',
      email: '<EMAIL>',
      plan: 'Premium',
      amount: '$99',
      amountKSH: 'KSH 14,850',
      mpesaNumber: '+254712345678',
      status: 'completed',
      date: '2024-01-15 14:30',
      mpesaCode: 'QA12B3C4D5'
    },
    {
      id: 'TXN002',
      user: 'Jane Smith',
      email: '<EMAIL>',
      plan: 'Basic',
      amount: '$29',
      amountKSH: 'KSH 4,350',
      mpesaNumber: '+254723456789',
      status: 'pending',
      date: '2024-01-16 09:15',
      mpesaCode: 'Pending'
    },
    {
      id: 'TXN003',
      user: 'Mike Johnson',
      email: '<EMAIL>',
      plan: 'VIP',
      amount: '$199',
      amountKSH: 'KSH 29,850',
      mpesaNumber: '+254734567890',
      status: 'failed',
      date: '2024-01-16 16:45',
      mpesaCode: 'Failed'
    }
  ])

  const [messageData, setMessageData] = useState({
    recipient: 'all',
    subject: '',
    message: '',
    sendToTelegram: false
  })

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-100'
      case 'expired': return 'text-red-600 bg-red-100'
      case 'pending': return 'text-yellow-600 bg-yellow-100'
      case 'completed': return 'text-green-600 bg-green-100'
      case 'failed': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const handleUserAction = (userId, action) => {
    setUsers(users.map(user => {
      if (user.id === userId) {
        switch (action) {
          case 'activate':
            return { ...user, status: 'active', telegramStatus: 'added' }
          case 'deactivate':
            return { ...user, status: 'expired', telegramStatus: 'removed' }
          case 'addToTelegram':
            return { ...user, telegramStatus: 'added' }
          case 'removeFromTelegram':
            return { ...user, telegramStatus: 'removed' }
          default:
            return user
        }
      }
      return user
    }))
  }

  const handlePackageUpdate = (packageId, field, value) => {
    setPackages(packages.map(pkg => 
      pkg.id === packageId ? { ...pkg, [field]: value } : pkg
    ))
  }

  const handleSendMessage = (e) => {
    e.preventDefault()
    console.log('Sending message:', messageData)
    alert('Message sent successfully!')
    setMessageData({ recipient: 'all', subject: '', message: '', sendToTelegram: false })
  }

  const handleLogout = () => {
    logout()
    navigate('/admin')
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Admin Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              {/* Mobile menu button */}
              <button
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="lg:hidden mr-4 p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
              <div className="flex-shrink-0">
                <h1 className="text-2xl font-bold text-purple-600">ForexClass Admin</h1>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="hidden sm:block text-sm text-gray-600">
                Welcome, <span className="font-medium">{user?.firstName}</span>
              </div>
              <button
                onClick={() => navigate('/')}
                className="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium"
              >
                <span className="hidden sm:inline">View Website</span>
                <span className="sm:hidden">Website</span>
              </button>
              <button
                onClick={handleLogout}
                className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 text-sm font-medium"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Dashboard Header */}
        <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-2xl p-8 mb-8">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-4xl font-bold mb-2">Dashboard</h2>
              <p className="text-purple-100">Manage your ForexClass platform</p>
            </div>
            <div className="text-right">
              <div className="text-sm opacity-80">Current Exchange Rate</div>
              <div className="text-2xl font-bold">1 USD = {exchangeRate.toFixed(2)} KSH</div>
            </div>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 mb-8">
          <div className="bg-white rounded-xl p-6 text-center shadow-sm">
            <div className="text-3xl font-bold text-blue-600 mb-2">{stats.totalUsers}</div>
            <div className="text-sm text-gray-600">Total Users</div>
          </div>
          <div className="bg-white rounded-xl p-6 text-center shadow-sm">
            <div className="text-3xl font-bold text-green-600 mb-2">{stats.activeSubscriptions}</div>
            <div className="text-sm text-gray-600">Active Subscriptions</div>
          </div>
          <div className="bg-white rounded-xl p-6 text-center shadow-sm">
            <div className="text-3xl font-bold text-purple-600 mb-2">${stats.totalRevenue.toLocaleString()}</div>
            <div className="text-sm text-gray-600">Total Revenue</div>
          </div>
          <div className="bg-white rounded-xl p-6 text-center shadow-sm">
            <div className="text-3xl font-bold text-yellow-600 mb-2">{stats.pendingTransactions}</div>
            <div className="text-sm text-gray-600">Pending Payments</div>
          </div>
          <div className="bg-white rounded-xl p-6 text-center shadow-sm">
            <div className="text-3xl font-bold text-indigo-600 mb-2">{stats.telegramMembers}</div>
            <div className="text-sm text-gray-600">Telegram Members</div>
          </div>
          <div className="bg-white rounded-xl p-6 text-center shadow-sm">
            <div className="text-3xl font-bold text-emerald-600 mb-2">+{stats.monthlyGrowth}%</div>
            <div className="text-sm text-gray-600">Monthly Growth</div>
          </div>
        </div>

        {/* Main Content Area with Sidebar */}
        <div className="flex gap-8 relative">
          {/* Mobile Sidebar Overlay */}
          {sidebarOpen && (
            <div
              className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
              onClick={() => setSidebarOpen(false)}
            />
          )}

          {/* Sidebar Navigation */}
          <div className={`
            w-64 flex-shrink-0 transition-transform duration-300 ease-in-out z-50
            lg:translate-x-0 lg:static lg:block
            ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
            fixed lg:relative top-0 left-0 h-full lg:h-auto
          `}>
            <div className="bg-white rounded-xl shadow-sm p-6 h-full lg:h-auto">
              {/* Mobile close button */}
              <div className="lg:hidden flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold text-gray-900">Navigation</h3>
                <button
                  onClick={() => setSidebarOpen(false)}
                  className="p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <h3 className="hidden lg:block text-lg font-semibold text-gray-900 mb-6">Navigation</h3>

              <nav className="space-y-2">
                {[
                  { id: 'overview', name: 'Overview', icon: '📊', description: 'Dashboard overview' },
                  { id: 'users', name: 'User Management', icon: '👥', description: 'Manage users' },
                  { id: 'packages', name: 'Packages', icon: '📦', description: 'Pricing & plans' },
                  { id: 'transactions', name: 'Transactions', icon: '💳', description: 'Payment history' },
                  { id: 'telegram', name: 'Telegram', icon: '📱', description: 'Channel management' },
                  { id: 'messages', name: 'Messages', icon: '✉️', description: 'Send notifications' }
                ].map((item) => (
                  <button
                    key={item.id}
                    onClick={() => {
                      setActiveTab(item.id)
                      setSidebarOpen(false) // Close mobile menu after selection
                    }}
                    className={`w-full flex items-start p-4 rounded-lg text-left transition-all ${
                      activeTab === item.id
                        ? 'bg-purple-50 border-2 border-purple-200 text-purple-700'
                        : 'hover:bg-gray-50 border-2 border-transparent text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    <span className="text-2xl mr-3 flex-shrink-0">{item.icon}</span>
                    <div>
                      <div className="font-medium">{item.name}</div>
                      <div className="text-sm opacity-75 hidden sm:block">{item.description}</div>
                    </div>
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            <div className="bg-white rounded-xl shadow-sm">
              {/* Page Header */}
              <div className="border-b border-gray-200 px-8 py-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 flex items-center">
                      <span className="text-3xl mr-3">
                        {activeTab === 'overview' && '📊'}
                        {activeTab === 'users' && '👥'}
                        {activeTab === 'packages' && '📦'}
                        {activeTab === 'transactions' && '💳'}
                        {activeTab === 'telegram' && '📱'}
                        {activeTab === 'messages' && '✉️'}
                      </span>
                      {activeTab === 'overview' && 'Dashboard Overview'}
                      {activeTab === 'users' && 'User Management'}
                      {activeTab === 'packages' && 'Package Management'}
                      {activeTab === 'transactions' && 'Transaction Management'}
                      {activeTab === 'telegram' && 'Telegram Management'}
                      {activeTab === 'messages' && 'Message Center'}
                    </h2>
                    <p className="text-gray-600 mt-1">
                      {activeTab === 'overview' && 'Monitor your platform performance and activity'}
                      {activeTab === 'users' && 'Manage user accounts and subscriptions'}
                      {activeTab === 'packages' && 'Configure pricing plans and features'}
                      {activeTab === 'transactions' && 'Review payments and transaction history'}
                      {activeTab === 'telegram' && 'Control Telegram channel access and members'}
                      {activeTab === 'messages' && 'Send notifications and announcements'}
                    </p>
                  </div>
                  <div className="text-sm text-gray-500">
                    Last updated: {new Date().toLocaleTimeString()}
                  </div>
                </div>
              </div>

              {/* Page Content */}
              <div className="p-8">
            {/* Overview Tab */}
            {activeTab === 'overview' && (
              <div className="space-y-8">
                <div className="grid lg:grid-cols-2 gap-8">
                  {/* Recent Activity */}
                  <div className="bg-gray-50 rounded-xl p-6">
                    <h3 className="text-xl font-semibold mb-6 flex items-center">
                      <span className="mr-2">📈</span>
                      Recent Activity
                    </h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200">
                        <div>
                          <div className="font-medium text-green-800">New Premium Subscription</div>
                          <div className="text-sm text-green-600">John Doe - {formatUSDToKSH('$99')}</div>
                        </div>
                        <div className="text-sm text-green-500">2 min ago</div>
                      </div>
                      <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200">
                        <div>
                          <div className="font-medium text-blue-800">M-Pesa Payment Received</div>
                          <div className="text-sm text-blue-600">Transaction: QA12B3C4D5</div>
                        </div>
                        <div className="text-sm text-blue-500">5 min ago</div>
                      </div>
                      <div className="flex items-center justify-between p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                        <div>
                          <div className="font-medium text-yellow-800">Subscription Expiring Soon</div>
                          <div className="text-sm text-yellow-600">Jane Smith - 2 days remaining</div>
                        </div>
                        <div className="text-sm text-yellow-500">1 hour ago</div>
                      </div>
                    </div>
                  </div>

                  {/* Quick Actions */}
                  <div className="bg-gray-50 rounded-xl p-6">
                    <h3 className="text-xl font-semibold mb-6 flex items-center">
                      <span className="mr-2">⚡</span>
                      Quick Actions
                    </h3>
                    <div className="space-y-4">
                      <button 
                        onClick={() => setActiveTab('telegram')}
                        className="w-full bg-blue-600 text-white p-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center"
                      >
                        <span className="mr-2">📱</span>
                        Manage Telegram Channel
                      </button>
                      <button 
                        onClick={() => setActiveTab('messages')}
                        className="w-full bg-green-600 text-white p-4 rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center"
                      >
                        <span className="mr-2">✉️</span>
                        Send Broadcast Message
                      </button>
                      <button 
                        onClick={() => setActiveTab('packages')}
                        className="w-full bg-purple-600 text-white p-4 rounded-lg hover:bg-purple-700 transition-colors flex items-center justify-center"
                      >
                        <span className="mr-2">📦</span>
                        Update Package Prices
                      </button>
                      <button className="w-full bg-orange-600 text-white p-4 rounded-lg hover:bg-orange-700 transition-colors flex items-center justify-center">
                        <span className="mr-2">📊</span>
                        Export Analytics Report
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Users Tab */}
            {activeTab === 'users' && (
              <div className="space-y-6">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                  <h3 className="text-2xl font-semibold flex items-center">
                    <span className="mr-2">👥</span>
                    User Management
                  </h3>
                  <div className="flex gap-3">
                    <input
                      type="search"
                      placeholder="Search users..."
                      className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                    <button className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                      Add User
                    </button>
                  </div>
                </div>

                <div className="bg-white rounded-lg overflow-hidden shadow-sm">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">User Details</th>
                          <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Plan</th>
                          <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Status</th>
                          <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Expiry Date</th>
                          <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Telegram</th>
                          <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200">
                        {users.map((user) => (
                          <tr key={user.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4">
                              <div>
                                <div className="font-medium text-gray-900">{user.name}</div>
                                <div className="text-sm text-gray-600">{user.email}</div>
                                <div className="text-sm text-gray-600">{user.phone}</div>
                              </div>
                            </td>
                            <td className="px-6 py-4">
                              <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                                {user.plan}
                              </span>
                            </td>
                            <td className="px-6 py-4">
                              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(user.status)}`}>
                                {user.status}
                              </span>
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-900">{user.expiryDate}</td>
                            <td className="px-6 py-4">
                              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                                user.telegramStatus === 'added' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                              }`}>
                                {user.telegramStatus}
                              </span>
                            </td>
                            <td className="px-6 py-4">
                              <div className="flex gap-2">
                                <button
                                  onClick={() => handleUserAction(user.id, user.status === 'active' ? 'deactivate' : 'activate')}
                                  className={`px-3 py-1 text-xs rounded-lg font-medium transition-colors ${
                                    user.status === 'active' 
                                      ? 'bg-red-100 text-red-700 hover:bg-red-200' 
                                      : 'bg-green-100 text-green-700 hover:bg-green-200'
                                  }`}
                                >
                                  {user.status === 'active' ? 'Deactivate' : 'Activate'}
                                </button>
                                <button
                                  onClick={() => handleUserAction(user.id, user.telegramStatus === 'added' ? 'removeFromTelegram' : 'addToTelegram')}
                                  className={`px-3 py-1 text-xs rounded-lg font-medium transition-colors ${
                                    user.telegramStatus === 'added'
                                      ? 'bg-red-100 text-red-700 hover:bg-red-200'
                                      : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                                  }`}
                                >
                                  {user.telegramStatus === 'added' ? 'Remove' : 'Add'} TG
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}

            {/* Packages Tab */}
            {activeTab === 'packages' && (
              <div className="space-y-6">
                <h3 className="text-2xl font-semibold flex items-center">
                  <span className="mr-2">📦</span>
                  Package Management
                </h3>
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {packages.map((pkg) => (
                    <div key={pkg.id} className="bg-gray-50 rounded-xl p-6">
                      <div className="flex justify-between items-start mb-4">
                        <h4 className="text-xl font-bold">{pkg.name}</h4>
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${pkg.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                          {pkg.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Price (USD)</label>
                          <input
                            type="number"
                            value={pkg.price}
                            onChange={(e) => handlePackageUpdate(pkg.id, 'price', parseFloat(e.target.value))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg"
                          />
                          <div className="text-sm text-gray-500 mt-1">
                            {formatUSDToKSH(`$${pkg.price}`)}
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <button className="flex-1 bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700">
                            Update
                          </button>
                          <button
                            onClick={() => handlePackageUpdate(pkg.id, 'isActive', !pkg.isActive)}
                            className={`flex-1 py-2 rounded-lg ${pkg.isActive ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700'} text-white`}
                          >
                            {pkg.isActive ? 'Disable' : 'Enable'}
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Transactions Tab */}
            {activeTab === 'transactions' && (
              <div className="space-y-6">
                <h3 className="text-2xl font-semibold flex items-center">
                  <span className="mr-2">💳</span>
                  Transaction Management
                </h3>
                <div className="bg-white rounded-lg overflow-hidden shadow-sm">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Transaction</th>
                          <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">User</th>
                          <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Amount</th>
                          <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Status</th>
                          <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200">
                        {transactions.map((txn) => (
                          <tr key={txn.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4">
                              <div className="font-mono text-sm">{txn.id}</div>
                              <div className="text-sm text-gray-600">{txn.date}</div>
                            </td>
                            <td className="px-6 py-4">
                              <div className="font-medium">{txn.user}</div>
                              <div className="text-sm text-gray-600">{txn.email}</div>
                            </td>
                            <td className="px-6 py-4">
                              <div className="font-medium">{txn.amountKSH}</div>
                              <div className="text-sm text-gray-600">{txn.amount}</div>
                            </td>
                            <td className="px-6 py-4">
                              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(txn.status)}`}>
                                {txn.status}
                              </span>
                            </td>
                            <td className="px-6 py-4">
                              {txn.status === 'pending' && (
                                <div className="flex gap-2">
                                  <button className="bg-green-600 text-white px-3 py-1 text-sm rounded hover:bg-green-700">
                                    Approve
                                  </button>
                                  <button className="bg-red-600 text-white px-3 py-1 text-sm rounded hover:bg-red-700">
                                    Reject
                                  </button>
                                </div>
                              )}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}

            {/* Telegram Tab */}
            {activeTab === 'telegram' && (
              <div className="space-y-6">
                <h3 className="text-2xl font-semibold flex items-center">
                  <span className="mr-2">📱</span>
                  Telegram Management
                </h3>
                <div className="bg-blue-50 rounded-xl p-6">
                  <div className="grid md:grid-cols-3 gap-4 mb-6">
                    <div className="bg-white rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold text-blue-600">{stats.telegramMembers}</div>
                      <div className="text-sm text-gray-600">Total Members</div>
                    </div>
                    <div className="bg-white rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold text-green-600">{users.filter(u => u.telegramStatus === 'added').length}</div>
                      <div className="text-sm text-gray-600">Active</div>
                    </div>
                    <div className="bg-white rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold text-red-600">{users.filter(u => u.telegramStatus === 'removed').length}</div>
                      <div className="text-sm text-gray-600">Removed</div>
                    </div>
                  </div>
                  <div className="flex gap-4">
                    <button className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700">
                      Bulk Add Active Users
                    </button>
                    <button className="bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700">
                      Remove Expired Users
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Messages Tab */}
            {activeTab === 'messages' && (
              <div className="space-y-6">
                <h3 className="text-2xl font-semibold flex items-center">
                  <span className="mr-2">✉️</span>
                  Send Messages
                </h3>
                <div className="bg-gray-50 rounded-xl p-6">
                  <form onSubmit={handleSendMessage} className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Recipients</label>
                      <select
                        value={messageData.recipient}
                        onChange={(e) => setMessageData({...messageData, recipient: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg"
                      >
                        <option value="all">All Users</option>
                        <option value="active">Active Subscribers</option>
                        <option value="expired">Expired Subscribers</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                      <input
                        type="text"
                        value={messageData.subject}
                        onChange={(e) => setMessageData({...messageData, subject: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Message</label>
                      <textarea
                        value={messageData.message}
                        onChange={(e) => setMessageData({...messageData, message: e.target.value})}
                        rows={6}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg"
                        required
                      />
                    </div>
                    <button type="submit" className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700">
                      Send Message
                    </button>
                  </form>
                </div>
              </div>
            )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AdminPage
