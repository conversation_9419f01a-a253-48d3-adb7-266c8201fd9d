import React, { useState, useEffect } from 'react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { AuthProvider } from './context/AuthContext'
import Navbar from './components/Navbar'
import Hero from './components/Hero'
import Features from './components/Features'
import Courses from './components/Courses'
import Pricing from './components/Pricing'
import Contact from './components/Contact'
import BackToTop from './components/BackToTop'

// Create a client
const queryClient = new QueryClient()

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <div className="min-h-screen bg-gray-50">
          <Navbar />
          <main>
            <Hero />
            <Features />
            <Courses />
            <Pricing />
            <Contact />
          </main>
          <BackToTop />
        </div>
      </AuthProvider>
    </QueryClientProvider>
  )
}

export default App
